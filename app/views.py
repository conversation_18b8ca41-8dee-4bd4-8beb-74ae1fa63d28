from unicodedata import name
from django.shortcuts import render
from django.http import HttpResponse
from django.contrib.auth import authenticate, login, logout


def Main(request):

    return render(request, 'main.html')

def Home(request):
    return render(request, 'home.html')

def About(request):
    return render(request, 'about.html')

def Login(request):
    return render(request, 'login.html')

def Logout(request):
    # destroy user logout
    request.session.flush()
    # redirect to home page
    return render(request, 'home.html')
